"use client";

import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import { Settings2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

export function DataTableViewOptions({ table }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Settings2 className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[150px]">
        <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {table
          .getAllColumns()
          .filter(
            (column) =>
              column.getCanHide() &&
              column.id !== "select" &&
              column.id !== "actions"
          )
          .map((column) => {
            const isVisible = column.getIsVisible();
            console.log(
              `Column ${
                column.id
              }: visible=${isVisible}, canHide=${column.getCanHide()}`
            );
            return (
              <DropdownMenuCheckboxItem
                key={column.id}
                className="capitalize"
                checked={isVisible}
                onCheckedChange={(checked) => {
                  console.log(
                    `Setting visibility for column ${column.id} to: ${checked}`
                  );
                  const currentVisibility = table.getState().columnVisibility;
                  const newVisibility = {
                    ...currentVisibility,
                    [column.id]: checked,
                  };
                  table.setColumnVisibility(newVisibility);
                  console.log(`New visibility state:`, newVisibility);
                }}
              >
                {column.id}
              </DropdownMenuCheckboxItem>
            );
          })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
